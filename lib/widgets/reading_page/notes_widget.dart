import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/widgets/book_notes/book_notes_list.dart';
import 'package:flutter/material.dart';

import 'package:dasso_reader/models/book.dart';

class ReadingNotes extends StatelessWidget {
  const ReadingNotes({
    super.key,
    required this.book,
    this.backgroundColor,
    this.textColor,
  });

  final Book book;
  final Color? backgroundColor;
  final Color? textColor;

  @override
  Widget build(BuildContext context) {
    final txtColor = textColor ?? Theme.of(context).colorScheme.onSurface;

    return SizedBox(
      height: 0.6 * MediaQuery.of(context).size.height,
      child: Material(
        elevation: DesignSystem.getAdjustedElevation(
          DesignSystem.elevationM,
        ), // 4.0 with manufacturer adjustments - matches context menu and progress widget
        color: backgroundColor ?? Theme.of(context).colorScheme.surface,
        // No borderRadius to maintain sharp edges for shadow effects
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: DesignSystem.spaceM,
          ), // 16.0 (preserves exact spacing)
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Add consistent top padding for spacing
              const SizedBox(
                height: DesignSystem.spaceL,
              ), // 24.0 (preserves exact spacing)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Text(
                  L10n.of(context).navBar_notes,
                  style: TextStyle(
                    fontSize: DesignSystem.getAdjustedFontSize(
                      context,
                      DesignSystem.fontSizeL,
                    ),
                    fontWeight:
                        DesignSystem.getAdjustedFontWeight(FontWeight.bold),
                    color: txtColor,
                  ),
                ),
              ),
              Expanded(
                child: ListView(
                  children: [
                    BookNotesList(
                      book: book,
                      reading: true,
                      textColor: txtColor,
                      hideBookmarks: true,
                    ),
                  ],
                ),
              ),
              // Add bottom padding for consistent spacing
              const SizedBox(
                height: DesignSystem.spaceL,
              ), // 24.0 (preserves exact spacing)
            ],
          ),
        ),
      ),
    );
  }
}
